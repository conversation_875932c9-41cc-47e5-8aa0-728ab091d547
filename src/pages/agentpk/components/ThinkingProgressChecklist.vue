<template>
  <div class="thinking-progress-checklist">
    <div class="checklist-header">
      <div class="header-icon">📋</div>
      <div class="header-title">思考进度</div>
    </div>
    
    <div class="checklist-items">
      <div 
        v-for="(stage, index) in stages" 
        :key="stage.key"
        class="checklist-item"
        :class="{ 'completed': stage.completed, 'current': stage.current }"
      >
        <div class="item-icon">
          <div v-if="stage.completed" class="check-icon">✓</div>
          <div v-else-if="stage.current" class="loading-icon">
            <div class="spinner"></div>
          </div>
          <div v-else class="pending-icon">○</div>
        </div>
        <div class="item-text">{{ stage.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IProps {
  thinkingData: IThinkingData;
  isComplete?: boolean;
}

const props = defineProps<IProps>();

// 定义五个阶段
const stages = computed(() => {
  const items = props.thinkingData.items;
  const hasQuestion = items.some(item => item.type === 'question');
  const hasSearchQuestions = items.some(item => item.type === 'search_questions');
  const hasSearchResult = items.some(item => item.type === 'search_result');
  const isComplete = props.isComplete || false;
  
  return [
    {
      key: 'analyze',
      label: '分析',
      completed: hasQuestion,
      current: !hasQuestion && props.thinkingData.isLoading
    },
    {
      key: 'search',
      label: '搜索',
      completed: hasSearchQuestions,
      current: hasQuestion && !hasSearchQuestions && props.thinkingData.isLoading
    },
    {
      key: 'summarize',
      label: '汇总',
      completed: hasSearchResult,
      current: hasSearchQuestions && !hasSearchResult && props.thinkingData.isLoading
    },
    {
      key: 'synthesize',
      label: '合成',
      completed: isComplete,
      current: hasSearchResult && !isComplete && props.thinkingData.isLoading
    },
    {
      key: 'complete',
      label: '完成',
      completed: isComplete,
      current: false
    }
  ];
});
</script>

<style lang="scss" scoped>
.thinking-progress-checklist {
  width: 200px;
  background: linear-gradient(135deg, rgba(139, 126, 216, 0.05) 0%, rgba(183, 148, 246, 0.08) 100%);
  border: 1px solid rgba(139, 126, 216, 0.3);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  align-self: flex-end; // 向下对齐
  
  .checklist-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(139, 126, 216, 0.2);
    
    .header-icon {
      font-size: 18px;
    }
    
    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary, #333);
    }
  }
  
  .checklist-items {
    .checklist-item {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
      padding: 8px;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.completed {
        background: rgba(16, 185, 129, 0.1);
        
        .item-icon .check-icon {
          color: var(--success-color, #10B981);
          font-weight: bold;
          font-size: 16px;
        }
        
        .item-text {
          color: var(--success-color, #10B981);
          font-weight: 500;
        }
      }
      
      &.current {
        background: rgba(139, 126, 216, 0.1);
        
        .item-text {
          color: var(--primary-color, #8B7ED8);
          font-weight: 500;
        }
      }
      
      .item-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        .check-icon {
          color: var(--success-color, #10B981);
          font-weight: bold;
          font-size: 16px;
        }
        
        .loading-icon {
          .spinner {
            width: 14px;
            height: 14px;
            border: 2px solid rgba(139, 126, 216, 0.3);
            border-top: 2px solid var(--primary-color, #8B7ED8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }
        
        .pending-icon {
          color: var(--text-disabled, #ccc);
          font-size: 16px;
        }
      }
      
      .item-text {
        font-size: 18px;
        color: var(--text-secondary, #666);
        transition: color 0.3s ease;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
